#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<AUTHOR> 
@license          http://www.xxxxx.xyz
@lastmodify       2024年8月4日
模块说明: 分析0813早安-静态文件夹，匹配音频文件并生成命名建议
"""

import os
import re
from pathlib import Path

def extract_tail_number(filename):
    """提取文件名中-后的尾号"""
    # 文件名格式如: 1-21.png, 10-26.png
    match = re.search(r'-(\d+)', filename)
    return match.group(1) if match else None

def find_matching_audio(tail_number, audio_dir):
    """在音频文件夹中寻找对应的音频文件"""
    if not os.path.exists(audio_dir):
        return None

    for audio_file in os.listdir(audio_dir):
        if audio_file.endswith('.MP3') or audio_file.endswith('.mp3'):
            # 音频文件格式如: 0813DY_21.MP3
            audio_tail = extract_tail_number(audio_file.replace('_', '-'))  # 将_替换为-以匹配
            if audio_tail == tail_number:
                return audio_file
    return None

def get_existing_names(template_dir):
    """获取所有现有的模板名称"""
    existing_names = set()
    
    def scan_directory(path):
        for item in os.listdir(path):
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                # 如果是目录，添加目录名并递归扫描
                existing_names.add(item)
                scan_directory(item_path)
            else:
                # 如果是文件，添加文件名（去除扩展名）
                name_without_ext = os.path.splitext(item)[0]
                existing_names.add(name_without_ext)
    
    if os.path.exists(template_dir):
        scan_directory(template_dir)
    
    return existing_names

def generate_zen_names():
    """生成早安、禅意、国风方向的名称候选"""
    zen_names = [
        "晨曦禅心", "朝露静思", "清晨悟道", "晨钟暮鼓", "禅意晨光",
        "静心晨语", "晨风禅韵", "朝阳禅境", "清晨觉悟", "晨露禅音",
        "禅心晨起", "晨光静谧", "朝霞禅意", "清晨禅思", "晨曦悟性",
        "禅语晨风", "朝露禅心", "晨钟禅韵", "清晨禅境", "晨光禅意",
        "静谧晨曦", "禅心朝露", "晨风悟道", "朝阳禅语", "清晨禅音",
        "晨露静心", "禅意朝霞", "晨光悟性", "朝露禅境", "清晨禅韵",
        "晨曦静思", "禅心晨风", "朝阳悟道", "清晨禅语", "晨露禅意",
        "静心朝霞", "禅境晨光", "晨风禅心", "朝露悟性", "清晨静谧",
        "晨曦禅境", "禅意晨露", "朝阳静思", "清晨禅心", "晨光悟道",
        "静谧朝露", "禅语晨曦", "晨风静思", "朝霞禅心", "清晨悟性"
    ]
    return zen_names

def main():
    # 设置路径 - 使用绝对路径
    base_dir = "/Users/<USER>/Desktop/送祝福/祝福模板"
    static_dir = os.path.join(base_dir, "模板更新批次/0813早安/静态")
    audio_dir = os.path.join(base_dir, "模板更新批次/0813早安/0813音频")
    template_dir = os.path.join(base_dir, "已完成模板（按分类）")

    print(f"静态文件目录: {static_dir}")
    print(f"音频文件目录: {audio_dir}")
    print(f"模板目录: {template_dir}")
    print(f"静态目录存在: {os.path.exists(static_dir)}")
    print(f"音频目录存在: {os.path.exists(audio_dir)}")
    print(f"模板目录存在: {os.path.exists(template_dir)}")

    # 获取现有名称
    existing_names = get_existing_names(template_dir)
    print(f"发现 {len(existing_names)} 个现有模板名称")

    # 获取静态文件列表并排序
    static_files = []
    if os.path.exists(static_dir):
        for file in os.listdir(static_dir):
            if file.endswith('.png'):
                static_files.append(file)
        print(f"找到 {len(static_files)} 个PNG文件")
    else:
        print("静态文件目录不存在！")
        return

    # 按文件名排序
    static_files.sort()
    
    print("=" * 60)
    print("任务1：文件匹配列表")
    print("=" * 60)
    
    file_matches = []

    # 先列出所有音频文件用于调试
    print("音频文件列表:")
    if os.path.exists(audio_dir):
        for audio_file in os.listdir(audio_dir):
            if audio_file.endswith('.MP3') or audio_file.endswith('.mp3'):
                print(f"  {audio_file}")

    print("\nPNG文件分析:")
    for i, png_file in enumerate(static_files, 1):
        tail_number = extract_tail_number(png_file)
        print(f"  {png_file} -> 尾号: {tail_number}")
        if tail_number:
            matching_audio = find_matching_audio(tail_number, audio_dir)
            file_matches.append({
                'index': i,
                'png_file': png_file,
                'tail_number': tail_number,
                'audio_file': matching_audio or "未找到匹配音频"
            })
            print(f"{i:2d}. PNG: {png_file:<15} (尾号:{tail_number}) -> 音频: {matching_audio or '未找到匹配音频'}")
    
    print("\n" + "=" * 60)
    print("任务2：命名建议列表")
    print("=" * 60)
    
    # 生成禅意名称候选
    zen_candidates = generate_zen_names()
    
    # 为每个文件分配唯一的名称
    assigned_names = []
    used_names = set()
    
    for i, match in enumerate(file_matches):
        # 寻找一个未被使用且不与现有模板重名的名称
        for name in zen_candidates:
            if name not in used_names and name not in existing_names:
                assigned_names.append(name)
                used_names.add(name)
                break
        else:
            # 如果所有候选名称都被使用，生成一个新的
            base_name = f"晨曦禅韵{i+1:02d}"
            counter = 1
            new_name = base_name
            while new_name in used_names or new_name in existing_names:
                new_name = f"{base_name}_{counter}"
                counter += 1
            assigned_names.append(new_name)
            used_names.add(new_name)
    
    # 输出命名建议
    for i, (match, name) in enumerate(zip(file_matches, assigned_names), 1):
        print(f"{i:2d}. {match['png_file']:<15} -> 建议命名: {name}")
    
    print("\n" + "=" * 60)
    print("总结")
    print("=" * 60)
    print(f"共处理 {len(static_files)} 个PNG文件")
    print(f"成功匹配音频文件 {sum(1 for m in file_matches if m['audio_file'] != '未找到匹配音频')} 个")
    print(f"生成唯一命名建议 {len(assigned_names)} 个")
    print(f"所有建议名称均与现有 {len(existing_names)} 个模板名称不重复")

if __name__ == "__main__":
    main()
