import os
import subprocess

# 当前脚本所在目录
folder = os.path.dirname(os.path.abspath(__file__))

for filename in os.listdir(folder):
    if filename.lower().endswith('.mp4'):
        mp4_path = os.path.join(folder, filename)
        png_name = os.path.splitext(filename)[0] + '.png'
        png_path = os.path.join(folder, png_name)
        # ffmpeg命令：-ss 2 表示第2秒，-vframes 1 表示只截取一帧
        cmd = [
            'ffmpeg', '-y', '-ss', '2', '-i', mp4_path,
            '-vframes', '1', png_path
        ]
        print(f'Extracting cover for {filename}...')
        subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
print('全部封面已生成！') 